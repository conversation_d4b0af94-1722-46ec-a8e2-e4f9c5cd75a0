-- Script to consolidate duplicate services and merge their pricing tiers
-- This script will:
-- 1. Identify duplicate services by name
-- 2. Keep the service with the highest price as the primary service
-- 3. Merge pricing tiers from all duplicate services into the primary service
-- 4. Update any bookings to reference the primary service
-- 5. Delete the duplicate services

-- Start transaction
BEGIN;

-- Consolidate "Airbrush Face & Body Painting" services
-- Keep the service with ID c35117a3-a451-4884-8a29-c930372d8463 (price $85) as primary
-- Merge pricing tiers from 734148fa-9137-4e65-8510-4782f915a25d (price $25)

-- First, let's check current pricing tiers for both services
SELECT 
  s.id, 
  s.name, 
  s.price, 
  s.duration,
  spt.name as tier_name,
  spt.price as tier_price,
  spt.duration as tier_duration,
  spt.is_default
FROM services s
LEFT JOIN service_pricing_tiers spt ON s.id = spt.service_id
WHERE s.name = 'Airbrush Face & Body Painting'
ORDER BY s.price DESC, spt.sort_order;

-- Update pricing tiers from the duplicate service to point to the primary service
UPDATE service_pricing_tiers 
SET service_id = 'c35117a3-a451-4884-8a29-c930372d8463'
WHERE service_id = '734148fa-9137-4e65-8510-4782f915a25d';

-- Update any bookings that reference the duplicate service
UPDATE bookings 
SET service_id = 'c35117a3-a451-4884-8a29-c930372d8463'
WHERE service_id = '734148fa-9137-4e65-8510-4782f915a25d';

-- Now we need to ensure we don't have duplicate pricing tiers
-- Let's create unique pricing tiers by consolidating similar ones
WITH consolidated_tiers AS (
  SELECT 
    service_id,
    name,
    description,
    duration,
    price,
    is_default,
    ROW_NUMBER() OVER (
      PARTITION BY service_id, name, duration, price 
      ORDER BY is_default DESC, sort_order
    ) as rn
  FROM service_pricing_tiers 
  WHERE service_id = 'c35117a3-a451-4884-8a29-c930372d8463'
)
DELETE FROM service_pricing_tiers 
WHERE service_id = 'c35117a3-a451-4884-8a29-c930372d8463'
AND id NOT IN (
  SELECT spt.id 
  FROM service_pricing_tiers spt
  INNER JOIN consolidated_tiers ct ON (
    spt.service_id = ct.service_id 
    AND spt.name = ct.name 
    AND spt.duration = ct.duration 
    AND spt.price = ct.price
    AND ct.rn = 1
  )
  WHERE spt.service_id = 'c35117a3-a451-4884-8a29-c930372d8463'
);

-- Update sort order for remaining pricing tiers
UPDATE service_pricing_tiers 
SET sort_order = subquery.new_sort_order
FROM (
  SELECT 
    id,
    ROW_NUMBER() OVER (ORDER BY is_default DESC, price ASC) - 1 as new_sort_order
  FROM service_pricing_tiers 
  WHERE service_id = 'c35117a3-a451-4884-8a29-c930372d8463'
) as subquery
WHERE service_pricing_tiers.id = subquery.id;

-- Ensure only one default tier exists
UPDATE service_pricing_tiers 
SET is_default = false 
WHERE service_id = 'c35117a3-a451-4884-8a29-c930372d8463';

UPDATE service_pricing_tiers 
SET is_default = true 
WHERE service_id = 'c35117a3-a451-4884-8a29-c930372d8463'
AND id = (
  SELECT id 
  FROM service_pricing_tiers 
  WHERE service_id = 'c35117a3-a451-4884-8a29-c930372d8463'
  ORDER BY sort_order 
  LIMIT 1
);

-- Delete the duplicate service
DELETE FROM services 
WHERE id = '734148fa-9137-4e65-8510-4782f915a25d';

-- Verify the consolidation
SELECT 
  s.id, 
  s.name, 
  s.price, 
  s.duration,
  COUNT(spt.id) as pricing_tiers_count
FROM services s
LEFT JOIN service_pricing_tiers spt ON s.id = spt.service_id
WHERE s.name = 'Airbrush Face & Body Painting'
GROUP BY s.id, s.name, s.price, s.duration;

-- Show final pricing tiers
SELECT 
  s.name as service_name,
  spt.name as tier_name,
  spt.duration as tier_duration,
  spt.price as tier_price,
  spt.is_default,
  spt.sort_order
FROM services s
INNER JOIN service_pricing_tiers spt ON s.id = spt.service_id
WHERE s.name = 'Airbrush Face & Body Painting'
ORDER BY spt.sort_order;

COMMIT;
