import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Image from 'next/image';
import { toast } from 'react-toastify';
import { getAuthToken } from '@/lib/auth-token-manager';
import styles from '@/styles/admin/inventory/ProductForm.module.css';

/**
 * ProductForm component for creating and editing products
 *
 * @param {Object} props - Component props
 * @param {Object} props.product - Product data for editing (null for new product)
 * @param {Function} props.onSave - Function to call when product is saved
 * @param {Function} props.onCancel - Function to call when form is cancelled
 * @returns {JSX.Element}
 */
export default function ProductForm({ product = null, onSave, onCancel }) {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [categories, setCategories] = useState([]);
  const [uploadingImages, setUploadingImages] = useState(false);

  // Form state
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    sku: '',
    price: '',
    sale_price: '',
    cost_price: '',
    category: '',
    stock: 0,
    low_stock_threshold: 5,
    status: 'active',
    featured: false,
    meta_title: '',
    meta_description: '',
    image_url: '',
    gallery_images: []
  });

  // Load product data if editing
  useEffect(() => {
    if (product) {
      setFormData({
        name: product.name || '',
        description: product.description || '',
        sku: product.sku || '',
        price: product.price || '',
        sale_price: product.sale_price || '',
        cost_price: product.cost_price || '',
        category: product.category || '',
        stock: product.stock || 0,
        low_stock_threshold: product.low_stock_threshold || 5,
        status: product.status || 'active',
        featured: product.featured || false,
        meta_title: product.meta_title || '',
        meta_description: product.meta_description || '',
        image_url: product.image_url || '',
        gallery_images: product.gallery_images || []
      });
    }
  }, [product]);

  // Load categories
  useEffect(() => {
    async function fetchCategories() {
      try {
        const response = await fetch('/api/admin/inventory/categories');
        if (!response.ok) {
          throw new Error('Failed to fetch categories');
        }
        const data = await response.json();
        setCategories(data.categories);
      } catch (err) {
        console.error('Error fetching categories:', err);
        toast.error('Failed to load categories');
      }
    }

    fetchCategories();
  }, []);

  // Handle form input changes
  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  // Handle numeric input changes
  const handleNumericChange = (e) => {
    const { name, value } = e.target;
    // Allow empty string or valid number
    if (value === '' || !isNaN(parseFloat(value))) {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  // Handle main image upload
  const handleMainImageUpload = async (e) => {
    const file = e.target.files[0];
    if (!file) return;

    const fileType = file.type.split('/')[1];
    const validTypes = ['jpeg', 'jpg', 'png', 'webp'];

    if (!validTypes.includes(fileType)) {
      toast.error('Please upload a valid image file (JPEG, PNG, WebP)');
      return;
    }

    setUploadingImages(true);

    try {
      const formData = new FormData();
      formData.append('image', file);

      // Get the current auth token
      const token = await getAuthToken();

      const response = await fetch('/api/admin/uploads/image', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token || ''}`
        },
        body: formData
      });

      if (!response.ok) {
        throw new Error('Failed to upload image');
      }

      const data = await response.json();

      setFormData(prev => ({
        ...prev,
        image_url: data.url
      }));

      toast.success('Image uploaded successfully');
    } catch (err) {
      console.error('Error uploading image:', err);
      toast.error('Failed to upload image');
    } finally {
      setUploadingImages(false);
    }
  };

  // Handle gallery image upload
  const handleGalleryImageUpload = async (e) => {
    const files = Array.from(e.target.files);
    if (files.length === 0) return;

    setUploadingImages(true);

    try {
      const uploadPromises = files.map(async (file) => {
        const fileType = file.type.split('/')[1];
        const validTypes = ['jpeg', 'jpg', 'png', 'webp'];

        if (!validTypes.includes(fileType)) {
          throw new Error(`Invalid file type: ${file.name}`);
        }

        const formData = new FormData();
        formData.append('image', file);

        // Get the current auth token
        const token = await getAuthToken();

        const response = await fetch('/api/admin/uploads/image', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token || ''}`
          },
          body: formData
        });

        if (!response.ok) {
          throw new Error(`Failed to upload: ${file.name}`);
        }

        const data = await response.json();
        return data.url;
      });

      const uploadedUrls = await Promise.all(uploadPromises);

      setFormData(prev => ({
        ...prev,
        gallery_images: [...prev.gallery_images, ...uploadedUrls]
      }));

      toast.success(`${uploadedUrls.length} images uploaded successfully`);
    } catch (err) {
      console.error('Error uploading gallery images:', err);
      toast.error('Failed to upload one or more images');
    } finally {
      setUploadingImages(false);
    }
  };

  // Remove gallery image
  const removeGalleryImage = (index) => {
    setFormData(prev => ({
      ...prev,
      gallery_images: prev.gallery_images.filter((_, i) => i !== index)
    }));
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    // Validate form
    if (!formData.name || !formData.price) {
      setError('Name and price are required');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Prepare data for API
      const productData = {
        ...formData,
        price: parseFloat(formData.price),
        sale_price: formData.sale_price ? parseFloat(formData.sale_price) : null,
        cost_price: formData.cost_price ? parseFloat(formData.cost_price) : null,
        stock: parseInt(formData.stock, 10),
        low_stock_threshold: parseInt(formData.low_stock_threshold, 10)
      };

      // Create or update product
      const url = product ? `/api/admin/inventory/products/${product.id}` : '/api/admin/inventory/products';
      const method = product ? 'PUT' : 'POST';

      // Get the current auth token
      const token = await getAuthToken();

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token || ''}`
        },
        body: JSON.stringify(productData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to save product');
      }

      const { data } = await response.json();

      toast.success(`Product ${product ? 'updated' : 'created'} successfully`);

      // Call onSave callback
      if (onSave) {
        onSave(data);
      }
    } catch (err) {
      console.error('Error saving product:', err);
      setError(err.message || 'Failed to save product');
      toast.error(err.message || 'Failed to save product');
    } finally {
      setLoading(false);
    }
  };

  return (
    <form className={styles.productForm} onSubmit={handleSubmit}>
      {error && <div className={styles.errorMessage}>{error}</div>}

      <div className={styles.formGrid}>
        {/* Basic Information */}
        <div className={styles.formSection}>
          <h3>Basic Information</h3>

          <div className={styles.formGroup}>
            <label htmlFor="name">Product Name *</label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleChange}
              required
              className={styles.formControl}
            />
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="description">Description</label>
            <textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleChange}
              rows={5}
              className={styles.formControl}
            />
          </div>

          <div className={styles.formRow}>
            <div className={styles.formGroup}>
              <label htmlFor="sku">SKU</label>
              <input
                type="text"
                id="sku"
                name="sku"
                value={formData.sku}
                onChange={handleChange}
                className={styles.formControl}
              />
            </div>

            <div className={styles.formGroup}>
              <label htmlFor="category">Category</label>
              <select
                id="category"
                name="category"
                value={formData.category}
                onChange={handleChange}
                className={styles.formControl}
              >
                <option value="">Select Category</option>
                {categories.map(category => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* Pricing and Inventory */}
        <div className={styles.formSection}>
          <h3>Pricing & Inventory</h3>

          <div className={styles.formRow}>
            <div className={styles.formGroup}>
              <label htmlFor="price">Price ($) *</label>
              <input
                type="text"
                id="price"
                name="price"
                value={formData.price}
                onChange={handleNumericChange}
                required
                className={styles.formControl}
              />
            </div>

            <div className={styles.formGroup}>
              <label htmlFor="sale_price">Sale Price ($)</label>
              <input
                type="text"
                id="sale_price"
                name="sale_price"
                value={formData.sale_price}
                onChange={handleNumericChange}
                className={styles.formControl}
              />
            </div>
          </div>

          <div className={styles.formRow}>
            <div className={styles.formGroup}>
              <label htmlFor="cost_price">Cost Price ($)</label>
              <input
                type="text"
                id="cost_price"
                name="cost_price"
                value={formData.cost_price}
                onChange={handleNumericChange}
                className={styles.formControl}
              />
            </div>

            <div className={styles.formGroup}>
              <label htmlFor="stock">Stock Quantity</label>
              <input
                type="number"
                id="stock"
                name="stock"
                value={formData.stock}
                onChange={handleNumericChange}
                min="0"
                className={styles.formControl}
              />
            </div>
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="low_stock_threshold">Low Stock Threshold</label>
            <input
              type="number"
              id="low_stock_threshold"
              name="low_stock_threshold"
              value={formData.low_stock_threshold}
              onChange={handleNumericChange}
              min="0"
              className={styles.formControl}
            />
            <small>Alert will be triggered when stock falls below this number</small>
          </div>
        </div>

        {/* Images */}
        <div className={styles.formSection}>
          <h3>Images</h3>

          <div className={styles.formGroup}>
            <label>Main Product Image</label>
            <div className={styles.imageUploadContainer}>
              {formData.image_url ? (
                <div className={styles.imagePreview}>
                  <img
                    src={formData.image_url}
                    alt="Product"
                    className={styles.previewImage}
                  />
                  <button
                    type="button"
                    className={styles.removeImageButton}
                    onClick={() => setFormData(prev => ({ ...prev, image_url: '' }))}
                  >
                    Remove
                  </button>
                </div>
              ) : (
                <div className={styles.uploadPlaceholder}>
                  <input
                    type="file"
                    id="main-image"
                    accept="image/jpeg,image/png,image/webp"
                    onChange={handleMainImageUpload}
                    disabled={uploadingImages}
                    className={styles.fileInput}
                  />
                  <label htmlFor="main-image" className={styles.uploadButton}>
                    {uploadingImages ? 'Uploading...' : 'Upload Image'}
                  </label>
                </div>
              )}
            </div>
          </div>

          <div className={styles.formGroup}>
            <label>Gallery Images</label>
            <div className={styles.galleryUploadContainer}>
              <div className={styles.galleryGrid}>
                {formData.gallery_images.map((url, index) => (
                  <div key={index} className={styles.galleryItem}>
                    <img
                      src={url}
                      alt={`Gallery ${index + 1}`}
                      className={styles.galleryImage}
                    />
                    <button
                      type="button"
                      className={styles.removeGalleryButton}
                      onClick={() => removeGalleryImage(index)}
                    >
                      ×
                    </button>
                  </div>
                ))}
                <div className={styles.galleryUploadPlaceholder}>
                  <input
                    type="file"
                    id="gallery-images"
                    accept="image/jpeg,image/png,image/webp"
                    onChange={handleGalleryImageUpload}
                    disabled={uploadingImages}
                    multiple
                    className={styles.fileInput}
                  />
                  <label htmlFor="gallery-images" className={styles.uploadButton}>
                    {uploadingImages ? 'Uploading...' : '+ Add Images'}
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Settings */}
      <div className={styles.formSection}>
        <h3>Settings</h3>

        <div className={styles.formGroup}>
          <label htmlFor="status">Status</label>
          <select
            id="status"
            name="status"
            value={formData.status}
            onChange={handleChange}
            className={styles.formControl}
          >
            <option value="active">Active</option>
            <option value="draft">Draft</option>
            <option value="archived">Archived</option>
          </select>
        </div>

        <div className={styles.formGroup}>
          <div className={styles.checkboxGroup}>
            <input
              type="checkbox"
              id="featured"
              name="featured"
              checked={formData.featured}
              onChange={handleChange}
              className={styles.checkbox}
            />
            <label htmlFor="featured">Featured Product</label>
          </div>
        </div>
      </div>

      {/* SEO */}
      <div className={styles.formSection}>
        <h3>SEO</h3>

        <div className={styles.formGroup}>
          <label htmlFor="meta_title">Meta Title</label>
          <input
            type="text"
            id="meta_title"
            name="meta_title"
            value={formData.meta_title}
            onChange={handleChange}
            className={styles.formControl}
          />
        </div>

        <div className={styles.formGroup}>
          <label htmlFor="meta_description">Meta Description</label>
          <textarea
            id="meta_description"
            name="meta_description"
            value={formData.meta_description}
            onChange={handleChange}
            rows={3}
            className={styles.formControl}
          />
        </div>
      </div>

      <div className={styles.formActions}>
        <button
          type="button"
          className={styles.cancelButton}
          onClick={onCancel}
          disabled={loading}
        >
          Cancel
        </button>
        <button
          type="submit"
          className={styles.saveButton}
          disabled={loading}
        >
          {loading ? 'Saving...' : (product ? 'Update Product' : 'Create Product')}
        </button>
      </div>
    </form>
  );
}
